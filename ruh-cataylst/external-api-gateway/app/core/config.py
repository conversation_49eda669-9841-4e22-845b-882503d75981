from typing import Any, Dict, Optional, Union

from pydantic import AnyUrl, PostgresDsn, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Application settings
    ENV: str = "dev"
    APP_NAME: str = "api-gateway"
    DEBUG: bool = False
    API_V1_STR: str = "/api/v1"

    # Kafka settings
    KAFKA_BROKER_HOST: str = "localhost"
    KAFKA_BROKER_PORT: str = "9092"

    # Rate limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_PERIOD: int = 60  # seconds

    # CORS settings
    CORS_ORIGINS: Union[list[str], str] = [
        "http://localhost",
        "http://localhost:3000",  # React default port
        "http://localhost:8000",  # API Gateway itself
    ]
    CORS_CREDENTIALS: bool = True
    CORS_METHODS: Union[list[str], str] = ["*"]
    CORS_HEADERS: Union[list[str], str] = ["*"]

    # Service discovery settings
    SERVICE_DISCOVERY_ENABLED: bool = False
    SERVICE_DISCOVERY_HOST: str = "consul"
    SERVICE_DISCOVERY_PORT: int = 8500

    # JWT settings
    JWT_SECRET_KEY: str = "your-secret-key-at-least-32-chars-long"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # Redis settings
    REDIS_HOST: str = ""
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: str = ""
    REDIS_JWT_ACCESS_EXPIRE_SEC: int = 3600
    REDIS_URI: Optional[str] = None  # Add this line

    # Server Authentication
    SERVER_AUTH_KEY: str = ""

    @validator("CORS_ORIGINS", pre=True)
    def parse_cors_origins(cls, v: Union[str, list[str]]) -> list[str]:
        if isinstance(v, str):
            # Handle wildcard
            if v.strip() == "*":
                return ["*"]
            # Handle comma-separated string
            if v.startswith('[') and v.endswith(']'):
                # It's already a JSON-like string, let pydantic handle it
                import json
                return json.loads(v)
            else:
                # Split by comma and strip whitespace
                return [origin.strip() for origin in v.split(',') if origin.strip()]
        return v

    @validator("CORS_METHODS", pre=True)
    def parse_cors_methods(cls, v: Union[str, list[str]]) -> list[str]:
        if isinstance(v, str):
            # Handle wildcard
            if v.strip() == "*":
                return ["*"]
            # Handle comma-separated string
            if v.startswith('[') and v.endswith(']'):
                # It's already a JSON-like string, let pydantic handle it
                import json
                return json.loads(v)
            else:
                # Split by comma and strip whitespace
                return [method.strip() for method in v.split(',') if method.strip()]
        return v

    @validator("CORS_HEADERS", pre=True)
    def parse_cors_headers(cls, v: Union[str, list[str]]) -> list[str]:
        if isinstance(v, str):
            # Handle wildcard
            if v.strip() == "*":
                return ["*"]
            # Handle comma-separated string
            if v.startswith('[') and v.endswith(']'):
                # It's already a JSON-like string, let pydantic handle it
                import json
                return json.loads(v)
            else:
                # Split by comma and strip whitespace
                return [header.strip() for header in v.split(',') if header.strip()]
        return v

    @validator("REDIS_URI", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v

        required = {"REDIS_HOST", "REDIS_PORT", "REDIS_DB"}
        missing = required - values.keys()
        if missing:
            raise ValueError(f"Missing required Redis configuration: {missing}")

        auth_part = f":{values.get('REDIS_PASSWORD')}@" if values.get("REDIS_PASSWORD") else ""
        return f"redis://{auth_part}{values.get('REDIS_HOST')}:{values.get('REDIS_PORT', 6379)}/{values.get('REDIS_DB', 0)}"

    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
